package hue.edu.xiong.volunteer_travel.service;

import hue.edu.xiong.volunteer_travel.core.Result;
import hue.edu.xiong.volunteer_travel.core.ResultGenerator;
import hue.edu.xiong.volunteer_travel.core.ServiceException;
import hue.edu.xiong.volunteer_travel.model.*;
import hue.edu.xiong.volunteer_travel.repository.*;
import hue.edu.xiong.volunteer_travel.util.CookieUitl;
import hue.edu.xiong.volunteer_travel.util.IdGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thymeleaf.util.StringUtils;

import javax.persistence.criteria.Predicate;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

@Service
public class ReserveService {

    @Autowired
    private HotelRepository hotelRepository;

    @Autowired
    private AttractionsRepository attractionsRepository;
    @Autowired
    private UserRepository userRepository;

    @Autowired
    private UserHotelRepository userHotelRepository;

    @Autowired
    private UserAttractionsRepository userAttractionsRepository;

    @Autowired
    private HotelRoomTypeRepository hotelRoomTypeRepository;

    @Autowired
    private HotelInventoryRepository hotelInventoryRepository;

    @Autowired
    private AttractionsInventoryRepository attractionsInventoryRepository;

    @Autowired
    private UserHotelOrderRepository userHotelOrderRepository;

    @Autowired
    private UserAttractionsOrderRepository userAttractionsOrderRepository;

    @Autowired
    private InventoryService inventoryService;

    @Autowired
    JdbcTemplate jdbcTemplate;

    public Page<Hotel> reserveHotelListUI(String searchName, Pageable pageable) {
        //查询启用的酒店列表
        Page<Hotel> hotelPage = hotelRepository.findAll((root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            //status状态,查询状态为0,启动的酒店
            predicates.add((cb.equal(root.get("status"), 0)));
            //酒店name模糊查询
            if (!StringUtils.isEmpty(searchName)) {
                predicates.add((cb.like(root.get("name"), "%" + searchName + "%")));
            }
            query.where(predicates.toArray(new Predicate[]{}));
            query.orderBy(cb.desc(root.get("createDate")));
            return null;
        }, pageable);
        return hotelPage;
    }

    public Hotel findHotelById(String id) {
        return hotelRepository.findById(id).orElseThrow(() -> new ServiceException("酒店id错误!"));
    }

    public Page<Attractions> reserveAttractionsListUI(String searchName, Pageable pageable) {
        //查询启用的景点列表
        Page<Attractions> attractionsPage = attractionsRepository.findAll((root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            //status状态,查询状态为0,启动的景点
            predicates.add((cb.equal(root.get("status"), 0)));
            //景点name模糊查询
            if (!StringUtils.isEmpty(searchName)) {
                predicates.add((cb.like(root.get("name"), "%" + searchName + "%")));
            }
            query.where(predicates.toArray(new Predicate[]{}));
            query.orderBy(cb.desc(root.get("createDate")));
            return null;
        }, pageable);
        return attractionsPage;
    }

    public Attractions findAttractionsById(String id) {
        return attractionsRepository.findById(id).orElseThrow(() -> new ServiceException("景点id错误!"));
    }


    public List<UserHotel> getReserveHotelByUser(HttpServletRequest request) {
        Cookie cookie = CookieUitl.get(request, "username");
        if (cookie == null) {
            throw new ServiceException("未能获得正确的用户名");
        }
        User user = userRepository.findUserByUsername(cookie.getValue());
        return userHotelRepository.findUserHotelsByUser(user);
    }

    @Transactional(rollbackFor = Exception.class)
    public Result cancelReserve(HttpServletRequest request, String id) {
        Cookie cookie = CookieUitl.get(request, "username");
        if (cookie == null) {
            throw new ServiceException("用户没有登录!");
        }
        Hotel hotel = findHotelById(id);
        User user = userRepository.findUserByUsername(cookie.getValue());
        UserHotel userHotel = userHotelRepository.findUserHotelByHotelAndUser(hotel, user);
        //存在值就是取消预约.不存在值就是预约
        if (userHotel != null) {
            userHotelRepository.delete(userHotel);
        } else {
            UserHotel newUserHotel = new UserHotel();
            newUserHotel.setId(IdGenerator.id());
            newUserHotel.setCreateDate(new Date());
            newUserHotel.setUser(user);
            newUserHotel.setHotel(hotel);
            userHotelRepository.saveAndFlush(newUserHotel);
        }
        return ResultGenerator.genSuccessResult();
    }

    public Boolean isReserveHotel(HttpServletRequest request, String id) {
        Cookie cookie = CookieUitl.get(request, "username");
        if (cookie != null) {
            User user = userRepository.findUserByUsername(cookie.getValue());
            Hotel hotel = findHotelById(id);
            UserHotel userHotel = userHotelRepository.findUserHotelByHotelAndUser(hotel, user);
            //每个酒店只能预约一次
            if (userHotel != null) {
                return true;
            }
        }
        return false;
    }


    public List<UserAttractions> getReserveAttractionsByUser(HttpServletRequest request) {
        Cookie cookie = CookieUitl.get(request, "username");
        if (cookie == null) {
            throw new ServiceException("未能获得正确的用户名");
        }
        User user = userRepository.findUserByUsername(cookie.getValue());
        return userAttractionsRepository.findUserAttractionsByUser(user);
    }

    @Transactional(rollbackFor = Exception.class)
    public Result cancelAttractionsReserve(HttpServletRequest request, String id) {
        Cookie cookie = CookieUitl.get(request, "username");
        if (cookie == null) {
            throw new ServiceException("用户没有登录!");
        }
        Attractions attractions = findAttractionsById(id);
        User user = userRepository.findUserByUsername(cookie.getValue());
        UserAttractions userAttractions = userAttractionsRepository.findUserAttractionsByAttractionsAndUser(attractions, user);
        //存在值就是取消预约.不存在值就是预约
        if (userAttractions != null) {
            userAttractionsRepository.delete(userAttractions);
        } else {
            UserAttractions newUserAttractions = new UserAttractions();
            newUserAttractions.setId(IdGenerator.id());
            newUserAttractions.setCreateDate(new Date());
            newUserAttractions.setUser(user);
            newUserAttractions.setAttractions(attractions);
            userAttractionsRepository.saveAndFlush(newUserAttractions);
        }
        return ResultGenerator.genSuccessResult();
    }

    public Boolean isReserveAttractions(HttpServletRequest request, String id) {
        Cookie cookie = CookieUitl.get(request, "username");
        if (cookie != null) {
            User user = userRepository.findUserByUsername(cookie.getValue());
            Attractions attractions = findAttractionsById(id);
            UserAttractions userAttractions = userAttractionsRepository.findUserAttractionsByAttractionsAndUser(attractions, user);
            //每个景点只能预约一次
            if (userAttractions != null) {
                return true;
            }
        }
        return false;
    }

    public List<Hotel> getTop10Hotel() {
        PageRequest pageable = PageRequest.of(0, 10);
        //查询启用的酒店列表
        Page<Hotel> hotelPage = hotelRepository.findAll((root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            //status状态,查询状态为0,启动的酒店
            predicates.add((cb.equal(root.get("status"), 0)));
            query.where(predicates.toArray(new Predicate[]{}));
            query.orderBy(cb.desc(root.get("createDate")));
            return null;
        }, pageable);
        return hotelPage.getContent();
    }

    public List<Attractions> getTop10Attractions() {
        PageRequest pageable = PageRequest.of(0, 10);
        Page<Attractions> attractionsPage = attractionsRepository.findAll((root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            //status状态,查询状态为0,启动的景点
            predicates.add((cb.equal(root.get("status"), 0)));
            //景点name模糊查询
            query.where(predicates.toArray(new Predicate[]{}));
            query.orderBy(cb.desc(root.get("createDate")));
            return null;
        }, pageable);
        return attractionsPage.getContent();
    }

    /**
     * 获取用户的酒店订单
     */
    public List<UserHotelOrder> getUserHotelOrders(HttpServletRequest request) {
        Cookie cookie = CookieUitl.get(request, "username");
        if (cookie == null) {
            throw new ServiceException("未能获得正确的用户名");
        }
        User user = userRepository.findUserByUsername(cookie.getValue());
        return userHotelOrderRepository.findByUserAndOrderStatus(user, 0); // 获取未取消的订单
    }

    /**
     * 获取用户的景点订单
     */
    public List<UserAttractionsOrder> getUserAttractionsOrders(HttpServletRequest request) {
        Cookie cookie = CookieUitl.get(request, "username");
        if (cookie == null) {
            throw new ServiceException("未能获得正确的用户名");
        }
        User user = userRepository.findUserByUsername(cookie.getValue());
        return userAttractionsOrderRepository.findByUserAndOrderStatus(user, 0); // 获取未取消的订单
    }

    /**
     * 获取酒店房型列表
     */
    public List<HotelRoomType> getHotelRoomTypes(String hotelId) {
        Hotel hotel = findHotelById(hotelId);
        return hotelRoomTypeRepository.findByHotel(hotel);
    }

    /**
     * 获取酒店指定日期范围的库存
     */
    public List<HotelInventory> getHotelInventory(String hotelId, Date startDate, Date endDate) {
        // 确保库存已初始化
        ensureInventoryInitialized();

        return hotelInventoryRepository.findByHotelIdAndDateRange(hotelId, startDate, endDate);
    }

    /**
     * 获取景点指定日期范围的库存
     */
    public List<AttractionsInventory> getAttractionsInventoryByDateRange(String attractionsId, Date startDate, Date endDate) {
        // 确保库存已初始化
        ensureInventoryInitialized();

        return attractionsInventoryRepository.findByAttractionsIdAndDateRange(attractionsId, startDate, endDate);
    }

    /**
     * 获取景点指定日期的库存
     */
    public AttractionsInventory getAttractionsInventory(String attractionsId, Date date) {
        // 确保库存已初始化
        ensureInventoryInitialized();

        Attractions attractions = findAttractionsById(attractionsId);
        return attractionsInventoryRepository.findByAttractionsAndInventoryDate(attractions, date);
    }

    /**
     * 确保库存已初始化
     */
    private void ensureInventoryInitialized() {
        try {
            // 检查是否有库存记录，如果没有则初始化
            long hotelCount = hotelInventoryRepository.count();
            long attractionsCount = attractionsInventoryRepository.count();

            System.out.println("Current inventory counts - Hotel: " + hotelCount + ", Attractions: " + attractionsCount);

            if (hotelCount == 0 || attractionsCount == 0) {
                System.out.println("Initializing inventory from ReserveService...");
                inventoryService.manualInitInventory();
            }
        } catch (Exception e) {
            System.err.println("Error ensuring inventory is initialized: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 预订酒店
     */
//    @Transactional(rollbackFor = Exception.class)
    public Result reserveHotel(HttpServletRequest request, String hotelId, String roomTypeId,
                              String checkInDateStr, String checkOutDateStr, Integer roomCount) {
        if (roomCount <= 0) {
            return ResultGenerator.genFailResult("预订房间数必须大于0");
        }

        try {
            // 获取用户信息
            Cookie cookie = CookieUitl.get(request, "username");
            if (cookie == null) {
                throw new ServiceException("用户没有登录!");
            }
            User user = userRepository.findUserByUsername(cookie.getValue());

            // 获取酒店和房型信息
            Hotel hotel = findHotelById(hotelId);
            HotelRoomType roomType = hotelRoomTypeRepository.findById(roomTypeId)
                    .orElseThrow(() -> new ServiceException("房型ID错误!"));

            // 解析日期
            Date checkInDate = parseDate(checkInDateStr);
            Date checkOutDate = parseDate(checkOutDateStr);

            // 验证日期
            validateDateRange(checkInDate, checkOutDate);

            // 计算总价并检查库存
            BigDecimal totalPrice = BigDecimal.ZERO;
            List<HotelInventory> inventories = new ArrayList<>();

            Calendar calendar = Calendar.getInstance();
            calendar.setTime(checkInDate);

            while (calendar.getTime().before(checkOutDate)) {
                Date currentDate = calendar.getTime();

                // 获取当天库存
                // 先将日期格式化为年月日，去除时分秒
                Calendar cal = Calendar.getInstance();
                cal.setTime(currentDate);
                cal.set(Calendar.HOUR_OF_DAY, 0);
                cal.set(Calendar.MINUTE, 0);
                cal.set(Calendar.SECOND, 0);
                cal.set(Calendar.MILLISECOND, 0);
                Date normalizedDate = cal.getTime();

                System.out.println("Searching inventory for date: " + normalizedDate);

                // 直接使用精确的查询方法，按酒店、房型和日期查询
                HotelInventory inventory = hotelInventoryRepository.findByHotelAndRoomTypeAndInventoryDate(hotel, roomType, normalizedDate);

                if (inventory == null) {
                    System.out.println("No inventory found for date: " + normalizedDate + ", hotel: " + hotel.getId() + ", roomType: " + roomType.getId());

                    // 如果库存不存在，尝试创建
                    try {
                        // 使用改进后的初始化方法
                        inventory = inventoryService.initSingleHotelInventory(hotel, roomType, normalizedDate);

                        if (inventory == null) {
                            throw new ServiceException("初始化库存失败，所选日期没有可用库存!");
                        }
                    } catch (Exception e) {
                        System.err.println("Error creating inventory: " + e.getMessage());
                        // 最后再次尝试查询，可能是并发情况下已被其他线程创建
                        HotelInventory existingInventory = hotelInventoryRepository.findByHotelAndRoomTypeAndInventoryDate(hotel, roomType, normalizedDate);
                        if (existingInventory != null) {
                            inventory = existingInventory;
                        } else {
                            throw new ServiceException("所选日期没有可用库存: " + e.getMessage());
                        }
                    }
                }

                // 检查库存是否足够
                if (inventory.getAvailableInventory() < roomCount) {
                    throw new ServiceException(formatDate(currentDate) + " 库存不足，剩余: " + inventory.getAvailableInventory());
                }

                // 累加价格
                totalPrice = totalPrice.add(inventory.getPrice().multiply(new BigDecimal(roomCount)));

                // 添加到待更新列表
                inventories.add(inventory);

                // 下一天
                calendar.add(Calendar.DAY_OF_MONTH, 1);
            }

            // 创建订单
            UserHotelOrder order = new UserHotelOrder();
            order.setId(IdGenerator.id());
            order.setUser(user);
            order.setHotel(hotel);
            order.setRoomType(roomType);
            order.setCheckInDate(checkInDate);
            order.setCheckOutDate(checkOutDate);
            order.setRoomCount(roomCount);
            order.setTotalPrice(totalPrice);
            order.setOrderStatus(0); // 0-已预订
            order.setCreateDate(new Date());

            userHotelOrderRepository.save(order);

            // 更新库存，使用直接SQL更新避免并发问题
            for (HotelInventory inventory : inventories) {
                String updateSql = "UPDATE hotel_inventory SET available_inventory = available_inventory - ?, update_date = ? WHERE id = ? AND available_inventory >= ?";
                int updatedRows = jdbcTemplate.update(updateSql, roomCount, new Date(), inventory.getId(), roomCount);

                if (updatedRows == 0) {
                    // 如果没有更新任何行，可能是库存不足或者并发问题
                    throw new ServiceException("更新库存失败，可能是库存不足或者并发问题");
                }
            }

            return ResultGenerator.genSuccessResult(order);

        } catch (ServiceException e) {
            return ResultGenerator.genFailResult(e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            return ResultGenerator.genFailResult("预订失败: " + e.getMessage());
        }
    }

    /**
     * 预订景点
     */
    @Transactional(rollbackFor = Exception.class)
    public Result reserveAttractions(HttpServletRequest request, String attractionsId,
                                    String visitDateStr, Integer ticketCount) {
        if (ticketCount <= 0) {
            return ResultGenerator.genFailResult("预订门票数必须大于0");
        }

        try {
            // 获取用户信息
            Cookie cookie = CookieUitl.get(request, "username");
            if (cookie == null) {
                throw new ServiceException("用户没有登录!");
            }
            User user = userRepository.findUserByUsername(cookie.getValue());

            // 获取景点信息
            Attractions attractions = findAttractionsById(attractionsId);

            // 解析日期
            Date visitDate = parseDate(visitDateStr);

            // 验证日期
            validateDate(visitDate);

            // 获取当天库存
            // 先将日期格式化为年月日，去除时分秒
            Calendar cal = Calendar.getInstance();
            cal.setTime(visitDate);
            cal.set(Calendar.HOUR_OF_DAY, 0);
            cal.set(Calendar.MINUTE, 0);
            cal.set(Calendar.SECOND, 0);
            cal.set(Calendar.MILLISECOND, 0);
            Date normalizedDate = cal.getTime();

            System.out.println("Searching attractions inventory for date: " + normalizedDate);

            // 直接使用精确的查询方法
            AttractionsInventory inventory = attractionsInventoryRepository.findByAttractionsAndInventoryDate(attractions, normalizedDate);

            if (inventory == null) {
                System.out.println("No inventory found for date: " + normalizedDate + ", attractions: " + attractions.getId());

                // 如果库存不存在，尝试创建
                try {
                    // 使用改进后的初始化方法
                    inventory = inventoryService.initSingleAttractionsInventory(attractions, normalizedDate);

                    if (inventory == null) {
                        throw new ServiceException("初始化景点库存失败，所选日期没有可用库存!");
                    }
                } catch (Exception e) {
                    System.err.println("Error creating attractions inventory: " + e.getMessage());
                    // 最后再次尝试查询，可能是并发情况下已被其他线程创建
                    AttractionsInventory existingInventory = attractionsInventoryRepository.findByAttractionsAndInventoryDate(attractions, normalizedDate);
                    if (existingInventory != null) {
                        inventory = existingInventory;
                    } else {
                        throw new ServiceException("所选日期没有可用库存: " + e.getMessage());
                    }
                }
            }

            // 检查库存是否足够
            if (inventory.getAvailableInventory() < ticketCount) {
                throw new ServiceException("库存不足，剩余: " + inventory.getAvailableInventory());
            }

            // 计算总价
            BigDecimal totalPrice = inventory.getPrice().multiply(new BigDecimal(ticketCount));

            // 创建订单
            UserAttractionsOrder order = new UserAttractionsOrder();
            order.setId(IdGenerator.id());
            order.setUser(user);
            order.setAttractions(attractions);
            order.setVisitDate(visitDate);
            order.setTicketCount(ticketCount);
            order.setTotalPrice(totalPrice);
            order.setOrderStatus(0); // 0-已预订
            order.setCreateDate(new Date());

            userAttractionsOrderRepository.save(order);

            // 更新库存，使用直接SQL更新避免并发问题
            String updateSql = "UPDATE attractions_inventory SET available_inventory = available_inventory - ?, update_date = ? WHERE id = ? AND available_inventory >= ?";
            int updatedRows = jdbcTemplate.update(updateSql, ticketCount, new Date(), inventory.getId(), ticketCount);

            if (updatedRows == 0) {
                // 如果没有更新任何行，可能是库存不足或者并发问题
                throw new ServiceException("更新景点库存失败，可能是库存不足或者并发问题");
            }

            return ResultGenerator.genSuccessResult(order);

        } catch (ServiceException e) {
            return ResultGenerator.genFailResult(e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            return ResultGenerator.genFailResult("预订失败: " + e.getMessage());
        }
    }

    /**
     * 取消酒店订单
     */
    @Transactional(rollbackFor = Exception.class)
    public Result cancelHotelOrder(HttpServletRequest request, String orderId) {
        try {
            // 获取用户信息
            Cookie cookie = CookieUitl.get(request, "username");
            if (cookie == null) {
                throw new ServiceException("用户没有登录!");
            }
            User user = userRepository.findUserByUsername(cookie.getValue());

            // 获取订单
            UserHotelOrder order = userHotelOrderRepository.findById(orderId)
                    .orElseThrow(() -> new ServiceException("订单ID错误!"));

            // 验证订单属于当前用户
            if (!order.getUser().getId().equals(user.getId())) {
                throw new ServiceException("无权操作此订单!");
            }

            // 检查订单状态
            if (order.getOrderStatus() != 0) {
                throw new ServiceException("订单已取消，不能重复操作!");
            }

            // 更新订单状态
            order.setOrderStatus(1); // 1-已取消
            order.setUpdateDate(new Date());
            userHotelOrderRepository.save(order);

            // 恢复库存
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(order.getCheckInDate());

            while (calendar.getTime().before(order.getCheckOutDate())) {
                Date currentDate = calendar.getTime();

                // 获取当天库存
                HotelInventory inventory = hotelInventoryRepository.findByHotelAndRoomTypeAndInventoryDate(
                        order.getHotel(), order.getRoomType(), currentDate);

                if (inventory != null) {
                    // 恢复库存，使用直接SQL更新避免并发问题
                    String updateSql = "UPDATE hotel_inventory SET available_inventory = available_inventory + ?, update_date = ? WHERE id = ?";
                    jdbcTemplate.update(updateSql, order.getRoomCount(), new Date(), inventory.getId());
                }

                // 下一天
                calendar.add(Calendar.DAY_OF_MONTH, 1);
            }

            return ResultGenerator.genSuccessResult();

        } catch (ServiceException e) {
            return ResultGenerator.genFailResult(e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            return ResultGenerator.genFailResult("取消订单失败: " + e.getMessage());
        }
    }

    /**
     * 取消景点订单
     */
    @Transactional(rollbackFor = Exception.class)
    public Result cancelAttractionsOrder(HttpServletRequest request, String orderId) {
        try {
            // 获取用户信息
            Cookie cookie = CookieUitl.get(request, "username");
            if (cookie == null) {
                throw new ServiceException("用户没有登录!");
            }
            User user = userRepository.findUserByUsername(cookie.getValue());

            // 获取订单
            UserAttractionsOrder order = userAttractionsOrderRepository.findById(orderId)
                    .orElseThrow(() -> new ServiceException("订单ID错误!"));

            // 验证订单属于当前用户
            if (!order.getUser().getId().equals(user.getId())) {
                throw new ServiceException("无权操作此订单!");
            }

            // 检查订单状态
            if (order.getOrderStatus() != 0) {
                throw new ServiceException("订单已取消，不能重复操作!");
            }

            // 更新订单状态
            order.setOrderStatus(1); // 1-已取消
            order.setUpdateDate(new Date());
            userAttractionsOrderRepository.save(order);

            // 恢复库存
            AttractionsInventory inventory = attractionsInventoryRepository.findByAttractionsAndInventoryDate(
                    order.getAttractions(), order.getVisitDate());

            if (inventory != null) {
                // 恢复库存，使用直接SQL更新避免并发问题
                String updateSql = "UPDATE attractions_inventory SET available_inventory = available_inventory + ?, update_date = ? WHERE id = ?";
                jdbcTemplate.update(updateSql, order.getTicketCount(), new Date(), inventory.getId());
            }

            return ResultGenerator.genSuccessResult();

        } catch (ServiceException e) {
            return ResultGenerator.genFailResult(e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            return ResultGenerator.genFailResult("取消订单失败: " + e.getMessage());
        }
    }

    /**
     * 解析日期字符串 (格式: yyyy-MM-dd)
     */
    private Date parseDate(String dateStr) {
        try {
            Calendar calendar = Calendar.getInstance();
            String[] parts = dateStr.split("-");
            calendar.set(Integer.parseInt(parts[0]), Integer.parseInt(parts[1]) - 1, Integer.parseInt(parts[2]), 0, 0, 0);
            calendar.set(Calendar.MILLISECOND, 0);

            // 输出日期信息用于调试
            System.out.println("Parsed date: " + calendar.getTime());
            return calendar.getTime();
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException("日期格式错误，正确格式: yyyy-MM-dd");
        }
    }

    /**
     * 格式化日期 (格式: yyyy-MM-dd)
     */
    private String formatDate(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.YEAR) + "-" +
               (calendar.get(Calendar.MONTH) + 1) + "-" +
               calendar.get(Calendar.DAY_OF_MONTH);
    }

    /**
     * 验证日期范围
     */
    private void validateDateRange(Date startDate, Date endDate) {
        // 检查开始日期是否早于结束日期
        if (startDate.after(endDate) || startDate.equals(endDate)) {
            throw new ServiceException("入住日期必须早于离店日期");
        }

        // 检查日期是否在允许范围内（当前日期到15天后）
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        Date today = calendar.getTime();

        calendar.add(Calendar.DAY_OF_MONTH, 15);
        Date maxDate = calendar.getTime();

        if (startDate.before(today)) {
            throw new ServiceException("入住日期不能早于今天");
        }

        if (endDate.after(maxDate)) {
            throw new ServiceException("离店日期不能超过15天后");
        }
    }

    /**
     * 验证单个日期
     */
    private void validateDate(Date date) {
        // 检查日期是否在允许范围内（当前日期到15天后）
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        Date today = calendar.getTime();

        calendar.add(Calendar.DAY_OF_MONTH, 15);
        Date maxDate = calendar.getTime();

        if (date.before(today)) {
            throw new ServiceException("游览日期不能早于今天");
        }

        if (date.after(maxDate)) {
            throw new ServiceException("游览日期不能超过15天后");
        }
    }
}
