spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
#    username: root
#    password: Ni<PERSON><PERSON>@123
#    url: ******************************************************************************************************************
#    username: zhaoyang
#    password: zy123456
    username: root
    password: <PERSON><PERSON><PERSON>@123
    url: ******************************************************************************************************************
#    username: root
#    password: 123123
#    url: *************************************************************************************************************
  #    ****************************************************************************
  jpa:
    show-sql: true
  thymeleaf:
    prefix: classpath:/templates/
    suffix: .html
    mode: LEGACYHTML5
    encoding: UTF-8
    cache: false

server:
  servlet:
    context-path: /travel
  port: 18080
